#!/bin/bash

# PropBolt Production Deployment Script with SSL Certificates
# This script deploys both backend and frontend to Google App Engine with automatic SSL setup

set -e  # Exit on any error

PROJECT_ID="gold-braid-458901-v2"
DOMAINS=("propbolt.com" "www.propbolt.com" "brain.propbolt.com" "api.propbolt.com" "admin.propbolt.com" "go.propbolt.com")

echo "🚀 Starting PropBolt Production Deployment..."
echo "Project ID: $PROJECT_ID"

# Set the project
echo "📋 Setting Google Cloud project..."
sudo gcloud config set project $PROJECT_ID

# Clean up Go dependencies
echo "🧹 Cleaning Go dependencies..."
sudo go mod tidy

# Build Next.js frontend
echo "🏗️  Building Next.js frontend..."
sudo npm run build

# Deploy backend (Go API) first
echo "🔧 Deploying Go backend to default service..."
sudo gcloud app deploy app.yaml --quiet --project=$PROJECT_ID

# Deploy frontend (Next.js) 
echo "🎨 Deploying Next.js frontend to frontend service..."
sudo gcloud app deploy frontend-app.yaml --quiet --project=$PROJECT_ID

# Deploy dispatch configuration
echo "🔀 Deploying dispatch routing configuration..."
sudo gcloud app deploy dispatch.yaml --quiet --project=$PROJECT_ID

# Set up SSL certificates for all domains
echo "🔒 Setting up SSL certificates for all domains..."

for domain in "${DOMAINS[@]}"; do
    echo "Setting up SSL for $domain..."
    
    # Check if certificate already exists
    if sudo gcloud app ssl-certificates list --project=$PROJECT_ID --format="value(displayName)" | grep -q "$domain"; then
        echo "✅ SSL certificate for $domain already exists"
    else
        echo "📜 Creating SSL certificate for $domain..."
        sudo gcloud app ssl-certificates create $domain-cert \
            --domains=$domain \
            --project=$PROJECT_ID \
            --quiet || echo "⚠️  Certificate creation for $domain may already be in progress"
    fi
    
    # Map domain to the appropriate service
    echo "🗺️  Mapping domain $domain..."
    if [[ "$domain" == "propbolt.com" || "$domain" == "www.propbolt.com" ]]; then
        # Frontend domains
        sudo gcloud app domain-mappings create $domain \
            --service=frontend \
            --project=$PROJECT_ID \
            --quiet || echo "✅ Domain mapping for $domain already exists"
    else
        # Backend domains (brain, api, admin, go)
        sudo gcloud app domain-mappings create $domain \
            --service=default \
            --project=$PROJECT_ID \
            --quiet || echo "✅ Domain mapping for $domain already exists"
    fi
done

# Enable required APIs
echo "🔌 Enabling required Google Cloud APIs..."
sudo gcloud services enable appengine.googleapis.com --project=$PROJECT_ID
sudo gcloud services enable cloudsql.googleapis.com --project=$PROJECT_ID
sudo gcloud services enable compute.googleapis.com --project=$PROJECT_ID
sudo gcloud services enable logging.googleapis.com --project=$PROJECT_ID
sudo gcloud services enable monitoring.googleapis.com --project=$PROJECT_ID

# Set up Cloud SQL proxy for secure database connections
echo "🗄️  Configuring Cloud SQL connection..."
sudo gcloud sql instances describe propbolt-postgres --project=$PROJECT_ID > /dev/null || {
    echo "❌ Cloud SQL instance 'propbolt-postgres' not found. Please create it first."
    exit 1
}

# Configure firewall rules for App Engine
echo "🔥 Configuring firewall rules..."
sudo gcloud compute firewall-rules create allow-app-engine-https \
    --allow tcp:443 \
    --source-ranges 0.0.0.0/0 \
    --description "Allow HTTPS traffic to App Engine" \
    --project=$PROJECT_ID \
    --quiet || echo "✅ Firewall rule already exists"

sudo gcloud compute firewall-rules create allow-app-engine-http \
    --allow tcp:80 \
    --source-ranges 0.0.0.0/0 \
    --description "Allow HTTP traffic to App Engine (redirects to HTTPS)" \
    --project=$PROJECT_ID \
    --quiet || echo "✅ Firewall rule already exists"

# Set up monitoring and logging
echo "📊 Setting up monitoring and logging..."
sudo gcloud logging sinks create propbolt-app-logs \
    bigquery.googleapis.com/projects/$PROJECT_ID/datasets/propbolt_logs \
    --log-filter='resource.type="gae_app"' \
    --project=$PROJECT_ID \
    --quiet || echo "✅ Logging sink already exists"

# Create health check endpoint
echo "❤️  Setting up health checks..."
# Health check is handled by the app.yaml configuration

# Display deployment information
echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📍 Your application is now available at:"
echo "   🌐 Frontend: https://propbolt.com"
echo "   🧠 Admin API: https://brain.propbolt.com"
echo "   🔌 User API: https://api.propbolt.com"
echo ""
echo "🔒 SSL certificates are being provisioned and may take up to 15 minutes to become active."
echo ""
echo "📊 Monitor your application:"
echo "   Logs: https://console.cloud.google.com/logs/query?project=$PROJECT_ID"
echo "   Monitoring: https://console.cloud.google.com/monitoring?project=$PROJECT_ID"
echo "   App Engine: https://console.cloud.google.com/appengine?project=$PROJECT_ID"
echo ""
echo "🔧 Useful commands:"
echo "   View logs: sudo gcloud app logs tail -s default --project=$PROJECT_ID"
echo "   View frontend logs: sudo gcloud app logs tail -s frontend --project=$PROJECT_ID"
echo "   Check SSL status: sudo gcloud app ssl-certificates list --project=$PROJECT_ID"
echo ""

# Test the deployment
echo "🧪 Testing deployment..."
echo "Testing backend health..."
curl -f https://brain.propbolt.com/health || echo "⚠️  Backend health check failed (may be normal during initial deployment)"

echo "Testing frontend..."
curl -f https://propbolt.com || echo "⚠️  Frontend check failed (may be normal during initial deployment)"

echo ""
echo "✅ Production deployment complete! Your PropBolt application is live with SSL certificates."
