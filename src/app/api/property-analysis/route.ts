import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.propbolt.com';

interface PropertyAnalysisRequest {
  address: string;
  propertyId?: string;
}

interface PropertyAnalysis {
  basicInfo: any;
  comparables: any[];
  liens: any[];
  ownerInfo: any;
  zoning: any;
  utilities: any;
  investment: any;
  risks: any[];
}

// Get property details from RealEstateAPI
async function getPropertyDetails(address: string): Promise<any> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/proxy/property-detail`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ address }),
    });

    if (!response.ok) {
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching property details:', error);
    return null;
  }
}

// Get property comparables
async function getPropertyComparables(address: string): Promise<any[]> {
  try {
    const [compsV3Response, compsV2Response] = await Promise.all([
      fetch(`${API_BASE_URL}/api/v1/proxy/property-comps-v3`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ address }),
      }),
      fetch(`${API_BASE_URL}/api/v1/proxy/property-comps-v2`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ address }),
      })
    ]);

    const compsV3 = compsV3Response.ok ? await compsV3Response.json() : null;
    const compsV2 = compsV2Response.ok ? await compsV2Response.json() : null;

    return [
      ...(compsV3?.comparables || []),
      ...(compsV2?.comparables || [])
    ];
  } catch (error) {
    console.error('Error fetching comparables:', error);
    return [];
  }
}

// Get liens information
async function getLiensInfo(address: string): Promise<any[]> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/proxy/involuntary-liens`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ address }),
    });

    if (!response.ok) {
      return [];
    }

    const data = await response.json();
    return data.liens || [];
  } catch (error) {
    console.error('Error fetching liens:', error);
    return [];
  }
}

// Get owner information
async function getOwnerInfo(address: string): Promise<any> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/proxy/skiptrace`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ address }),
    });

    if (!response.ok) {
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching owner info:', error);
    return null;
  }
}

// Get investment analysis from PropBolt API
async function getInvestmentAnalysis(address: string): Promise<any> {
  try {
    const response = await fetch(`${API_BASE_URL}/rentEstimate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        address,
        compPropStatus: true,
        distanceInMiles: 2.0
      }),
    });

    if (!response.ok) {
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching investment analysis:', error);
    return null;
  }
}

// Analyze zoning and development potential
function analyzeZoning(propertyDetails: any): any {
  const zoning = propertyDetails?.zoning || 'Unknown';
  
  const zoningAnalysis = {
    current: zoning,
    developmentPotential: 'Unknown',
    permittedUses: [] as string[],
    restrictions: [] as string[],
    buildingRequirements: {} as any
  };

  // Basic zoning analysis
  if (zoning.includes('R') || zoning.includes('Residential')) {
    zoningAnalysis.developmentPotential = 'Residential Development';
    zoningAnalysis.permittedUses = ['Single Family', 'Duplex', 'Townhomes'];
  } else if (zoning.includes('C') || zoning.includes('Commercial')) {
    zoningAnalysis.developmentPotential = 'Commercial Development';
    zoningAnalysis.permittedUses = ['Retail', 'Office', 'Mixed Use'];
  } else if (zoning.includes('A') || zoning.includes('Agricultural')) {
    zoningAnalysis.developmentPotential = 'Agricultural/Rural';
    zoningAnalysis.permittedUses = ['Farming', 'Livestock', 'Rural Residential'];
  }

  return zoningAnalysis;
}

// Assess utilities availability
function assessUtilities(propertyDetails: any): any {
  return {
    water: propertyDetails?.utilities?.water || 'Unknown',
    sewer: propertyDetails?.utilities?.sewer || 'Unknown',
    electric: propertyDetails?.utilities?.electric || 'Unknown',
    gas: propertyDetails?.utilities?.gas || 'Unknown',
    internet: propertyDetails?.utilities?.internet || 'Unknown',
    estimatedConnectionCosts: {
      water: 5000,
      sewer: 8000,
      electric: 3000,
      gas: 2000
    }
  };
}

// Identify potential risks
function identifyRisks(propertyDetails: any, liens: any[]): any[] {
  const risks = [];

  if (liens.length > 0) {
    risks.push({
      type: 'Legal',
      severity: 'High',
      description: `${liens.length} lien(s) found on property`,
      details: liens
    });
  }

  if (propertyDetails?.floodZone) {
    risks.push({
      type: 'Environmental',
      severity: 'Medium',
      description: 'Property located in flood zone',
      details: propertyDetails.floodZone
    });
  }

  if (!propertyDetails?.roadAccess) {
    risks.push({
      type: 'Access',
      severity: 'High',
      description: 'No confirmed road access',
      details: 'Verify easements and access rights'
    });
  }

  return risks;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check account type
    if (session.user.accountType !== 'land' && session.user.accountType !== 'data') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const { address, propertyId }: PropertyAnalysisRequest = await request.json();

    if (!address) {
      return NextResponse.json({ error: 'Address is required' }, { status: 400 });
    }

    // Fetch all data in parallel
    const [
      propertyDetails,
      comparables,
      liens,
      ownerInfo,
      investmentAnalysis
    ] = await Promise.all([
      getPropertyDetails(address),
      getPropertyComparables(address),
      getLiensInfo(address),
      getOwnerInfo(address),
      getInvestmentAnalysis(address)
    ]);

    // Analyze the data
    const analysis: PropertyAnalysis = {
      basicInfo: propertyDetails,
      comparables,
      liens,
      ownerInfo,
      zoning: analyzeZoning(propertyDetails),
      utilities: assessUtilities(propertyDetails),
      investment: investmentAnalysis,
      risks: identifyRisks(propertyDetails, liens)
    };

    return NextResponse.json({
      success: true,
      address,
      analysis,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Property analysis error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
