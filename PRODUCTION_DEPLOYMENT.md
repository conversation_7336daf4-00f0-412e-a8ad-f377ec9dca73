# PropBolt Production Deployment Guide

## 🚀 Comprehensive Land Search Platform

This guide covers the complete production deployment of PropBolt's comprehensive land search platform with dual API integration, NextAuth authentication, and automatic SSL certificate provisioning.

## 📋 Architecture Overview

### **Multi-Service Architecture**
- **Frontend Service** (Next.js) - `propbolt.com`
- **Backend Service** (Go API) - `brain.propbolt.com`, `api.propbolt.com`
- **Database** - Google Cloud SQL PostgreSQL
- **SSL Certificates** - Automatic provisioning for all domains

### **Dual API Integration**
- **RealEstateAPI.com** - Professional real estate data (9 endpoints)
- **PropBolt Internal API** - Legacy Zillow integration with proxy rotation

## 🔧 Prerequisites

1. **Google Cloud CLI** installed and authenticated
2. **Node.js 20+** and **Go 1.22+**
3. **Project Access** to `gold-braid-458901-v2`
4. **Domain Configuration** for all PropBolt domains

## 🏗️ Quick Deployment

### **One-Command Production Deployment**

```bash
# Make deployment script executable
chmod +x deploy-production.sh

# Deploy everything with SSL certificates
sudo ./deploy-production.sh
```

This script automatically:
- ✅ Builds and deploys Go backend
- ✅ Builds and deploys Next.js frontend  
- ✅ Configures domain routing
- ✅ Provisions SSL certificates for all domains
- ✅ Sets up monitoring and logging
- ✅ Configures firewall rules
- ✅ Tests deployment health

## 🌐 Domain Configuration

### **Production Domains**
- `propbolt.com` → Frontend (Next.js)
- `brain.propbolt.com` → Admin API (Go)
- `api.propbolt.com` → User API (Go)
- `admin.propbolt.com` → Admin redirect (Go)
- `go.propbolt.com` → User redirect (Go)

### **SSL Certificates**
All domains automatically receive SSL certificates via Google App Engine managed certificates.

## 🔐 Authentication System

### **NextAuth.js Integration**
- **Admin Login**: `<EMAIL>` / `admin123`
- **Land User**: `<EMAIL>` / `land123`
- **Data User**: `<EMAIL>` / `user123`

### **Account Types**
- `land` - Access to land search dashboard
- `data` - Access to data analysis features
- `NULL` - Login prevented

## 🔍 Comprehensive Land Search Features

### **Phase 1: Dual API Integration** ✅
- **Property Discovery** - Multi-source search results
- **Financial Analysis** - Valuation and comparables
- **Legal Research** - Liens and owner information
- **Location Intelligence** - Maps and proximity data

### **Phase 2: Advanced Analysis** ✅
- **Development Potential** - Zoning and buildability
- **Investment Analysis** - ROI and market trends
- **Risk Assessment** - Environmental and legal risks
- **Utilities Assessment** - Infrastructure availability

### **Phase 3: User Experience** ✅
- **Interactive Dashboard** - Real-time search and filtering
- **Property Analysis Modal** - Comprehensive property reports
- **Map Integration** - Google Maps with property pins
- **Responsive Design** - Mobile and desktop optimized

## 📊 API Endpoints

### **Land Search API**
```
POST /api/land-search
GET /api/land-search?location=Daytona Beach, FL
```

### **Property Analysis API**
```
POST /api/property-analysis
```

### **Authentication API**
```
POST /api/auth/user
GET /api/auth/[...nextauth]
```

### **Health Check API**
```
GET /api/health
HEAD /api/health
```

## 🗄️ Database Configuration

### **Google Cloud SQL PostgreSQL**
- **Instance**: `propbolt-postgres`
- **Database**: `propbolt`
- **Connection**: SSL required
- **Backup**: Automatic daily backups

### **User Schema**
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  username VARCHAR(255) NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  role VARCHAR(50) DEFAULT 'user',
  account_type VARCHAR(50), -- 'land', 'data', or NULL
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📈 Monitoring and Logging

### **Google Cloud Monitoring**
- **Application Metrics** - Performance and usage
- **Error Tracking** - Real-time error monitoring
- **Uptime Monitoring** - Service availability

### **Logging Configuration**
- **Application Logs** - Structured JSON logging
- **Access Logs** - Request/response tracking
- **Error Logs** - Exception and error tracking

## 🔧 Manual Deployment Steps

If you need to deploy manually:

### **1. Backend Deployment**
```bash
# Clean dependencies
sudo go mod tidy

# Deploy Go backend
sudo gcloud app deploy app.yaml --project=gold-braid-458901-v2
```

### **2. Frontend Deployment**
```bash
# Build Next.js
sudo npm run build

# Deploy frontend
sudo gcloud app deploy frontend-app.yaml --project=gold-braid-458901-v2
```

### **3. Routing Configuration**
```bash
# Deploy dispatch rules
sudo gcloud app deploy dispatch.yaml --project=gold-braid-458901-v2
```

### **4. SSL Certificate Setup**
```bash
# Create SSL certificate
sudo gcloud app ssl-certificates create propbolt-cert \
  --domains=propbolt.com \
  --project=gold-braid-458901-v2

# Map domain
sudo gcloud app domain-mappings create propbolt.com \
  --service=frontend \
  --project=gold-braid-458901-v2
```

## 🧪 Testing the Deployment

### **Health Checks**
```bash
# Test backend
curl -f https://brain.propbolt.com/health

# Test frontend
curl -f https://propbolt.com/api/health
```

### **Authentication Test**
1. Visit `https://propbolt.com/land`
2. Login with `<EMAIL>` / `admin123`
3. Access land search dashboard

### **API Integration Test**
1. Search for "Daytona Beach, FL"
2. Verify dual API results (RealEstateAPI + PropBolt)
3. Click "View Full Analysis" on any property

## 🔄 Rollback Procedures

### **Quick Rollback**
```bash
# List versions
sudo gcloud app versions list --project=gold-braid-458901-v2

# Rollback to previous version
sudo gcloud app versions migrate [PREVIOUS_VERSION] --project=gold-braid-458901-v2
```

## 📞 Support and Maintenance

### **Useful Commands**
```bash
# View logs
sudo gcloud app logs tail -s default --project=gold-braid-458901-v2
sudo gcloud app logs tail -s frontend --project=gold-braid-458901-v2

# Check SSL status
sudo gcloud app ssl-certificates list --project=gold-braid-458901-v2

# Monitor resources
sudo gcloud app instances list --project=gold-braid-458901-v2
```

### **Troubleshooting**
- **SSL Issues**: Certificates can take up to 15 minutes to provision
- **Domain Mapping**: Verify DNS settings point to Google App Engine
- **Database Connection**: Check Cloud SQL instance status and firewall rules
- **API Errors**: Review application logs for detailed error messages

## 🎉 Success Metrics

After successful deployment, you should have:
- ✅ **Secure HTTPS** access to all domains
- ✅ **Dual API integration** working seamlessly
- ✅ **Authentication system** protecting all routes
- ✅ **Comprehensive land search** with Zillow-style features
- ✅ **Production monitoring** and logging active
- ✅ **Auto-scaling** and load balancing configured

---

**🚀 Your PropBolt comprehensive land search platform is now production-ready with enterprise-grade security, scalability, and monitoring!**
